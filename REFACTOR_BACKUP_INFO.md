# rclone后端重构备份信息

## 备份信息
- **备份时间**: 2025-08-01 02:14:35
- **备份路径**: `/Users/<USER>/rclone_backup_20250801_021435`
- **原始分支**: `master-115`
- **重构分支**: `refactor-backend-simplification`

## 重构前的配置选项记录

### 123网盘后端配置 (backend/123/123.go)
```go
type Options struct {
    // 认证配置
    ClientID     string `config:"client_id"`
    ClientSecret string `config:"client_secret"`
    Token        string `config:"token"`
    UserAgent    string `config:"user_agent"`
    RootFolderID string `config:"root_folder_id"`

    // 上传配置
    MaxUploadParts int `config:"max_upload_parts"`

    // 调速器配置 (将被简化)
    UploadPacerMinSleep   fs.Duration `config:"upload_pacer_min_sleep"`
    DownloadPacerMinSleep fs.Duration `config:"download_pacer_min_sleep"`
    StrictPacerMinSleep   fs.Duration `config:"strict_pacer_min_sleep"`

    // 编码配置
    Enc encoder.MultiEncoder `config:"encoding"`

    // 缓存配置
    CacheMaxSize       fs.SizeSuffix `config:"cache_max_size"`
    CacheTargetSize    fs.SizeSuffix `config:"cache_target_size"`
    CacheCleanupInterval fs.Duration `config:"cache_cleanup_interval"`
    EnableSmartCleanup bool          `config:"enable_smart_cleanup"`
}
```

### 115网盘后端配置 (backend/115/115.go)
```go
type Options struct {
    // 认证配置
    Cookie              string        `config:"cookie"`
    UserAgent           string        `config:"user_agent"`
    RootFolderID        string        `config:"root_folder_id"`
    AppID               string        `config:"app_id"`

    // 上传配置
    HashMemoryThreshold fs.SizeSuffix `config:"hash_memory_limit"`
    UploadHashOnly      bool          `config:"upload_hash_only"`
    OnlyStream          bool          `config:"only_stream"`
    FastUpload          bool          `config:"fast_upload"`
    NohashSize          fs.SizeSuffix `config:"nohash_size"`
    MaxUploadParts      int           `config:"max_upload_parts"`

    // 其他配置
    Internal  bool                 `config:"internal"`
    DualStack bool                 `config:"dual_stack"`
    NoCheck   bool                 `config:"no_check"`
    NoBuffer  bool                 `config:"no_buffer"`
    Enc       encoder.MultiEncoder `config:"encoding"`

    // 缓存配置
    CacheMaxSize       fs.SizeSuffix `config:"cache_max_size"`
    CacheTargetSize    fs.SizeSuffix `config:"cache_target_size"`
    CacheCleanupInterval fs.Duration `config:"cache_cleanup_interval"`
    EnableSmartCleanup bool          `config:"enable_smart_cleanup"`
}
```

## 重构前的架构问题记录

### backend/common/ 目录问题
- 包含伪通用组件，实际只为123/115网盘服务
- 重复实现了rclone已有功能
- 文件列表：
  - constants.go (常量定义过度细化)
  - utils.go (重复实现HTTP客户端)
  - network_optimizer.go (重复实现网络优化)
  - progress_tracker.go (重复实现进度跟踪)
  - cache_utils.go (重复实现缓存工具)

### 123网盘过度设计问题
- 4个独立的调速器 (listPacer, strictPacer, uploadPacer, downloadPacer)
- 过度细化的配置选项
- 自定义HTTP客户端管理
- 复杂的资源池设计

### 115网盘过度设计问题
- 双API客户端设计 (tradClient, openAPIClient)
- 臃肿的File结构体 (冗余字段)
- 过度复杂的下载URL管理
- 复杂的认证管理

## 重构目标
- 删除40-50%的冗余代码
- 降低70%的维护复杂度
- 完全符合rclone设计标准
- 保持100%的功能兼容性

## 验证检查点
- [x] 备份文件完整性验证
- [x] git分支创建成功
- [x] API接口文档记录完整
- [ ] 现有功能测试通过 (待执行)
