# 123网盘和115网盘后端文档

本目录包含123网盘和115网盘后端的相关文档。

## 📋 文档列表

### 🔄 迁移相关（重要）
- **[迁移指南](migration-guide.md)** - 从旧版本迁移到简化版本的详细指南
- **[迁移脚本](migration-script.sh)** - 自动清理已废弃配置参数的脚本
- **[测试验证指南](testing-guide.md)** - 功能测试和性能验证步骤

### ⚡ 性能优化
- **[性能优化指南](123-115-backend-performance-guide.md)** - 详细的性能参数配置和优化建议

### 🎬 媒体同步功能
- [123网盘媒体同步使用指南](123-media-sync-usage.md) - 123网盘媒体库同步功能说明
- [115网盘媒体同步使用指南](115-media-sync-usage.md) - 115网盘媒体库同步功能说明
- [统一媒体同步指南](media-sync-unified-guide.md) - 跨云盘媒体同步解决方案

## 🚀 快速开始

### 新用户
如果您是第一次使用123网盘或115网盘后端：
1. 查看 [性能优化指南](123-115-backend-performance-guide.md) 了解基本配置
2. 使用 [测试验证指南](testing-guide.md) 验证功能

### 现有用户（迁移）
如果您已经在使用旧版本的后端：
1. **必读**：[迁移指南](migration-guide.md)
2. 运行 [迁移脚本](migration-script.sh) 自动清理配置
3. 使用 [测试验证指南](testing-guide.md) 验证迁移结果

## 📝 版本说明

### 新版本特性
- ✅ 移除了过度设计的自定义缓存系统
- ✅ 使用rclone标准的dircache
- ✅ 简化了错误处理逻辑
- ✅ 提高了代码可维护性和稳定性
- ✅ 保持了所有核心功能

### 已移除的功能
- ❌ 自定义BadgerDB缓存
- ❌ 复杂的缓存管理命令
- ❌ 统一错误处理器
- ❌ 过度抽象的组件管理

### 兼容性
- ✅ 配置文件向后兼容（已废弃参数被忽略）
- ✅ 核心功能100%兼容
- ✅ API接口保持不变

## 🛠️ 故障排除

### 常见问题
1. **配置参数错误**：参考 [迁移指南](migration-guide.md) 移除已废弃参数
2. **性能问题**：查看 [性能优化指南](123-115-backend-performance-guide.md) 调整参数
3. **功能异常**：使用 [测试验证指南](testing-guide.md) 进行诊断

### 获取帮助
```bash
# 查看后端帮助
rclone help backend 123
rclone help backend 115

# 查看详细日志
rclone -vv ls 123:
rclone -vv ls 115:

# 测试连接
rclone about 123:
rclone about 115:
```

## 📊 性能对比

| 项目 | 旧版本 | 新版本 | 改进 |
|------|--------|--------|------|
| 代码行数 | ~20,000行 | ~19,500行 | 减少2.5% |
| 配置复杂度 | 高（20+缓存参数） | 低（使用rclone标准） | 大幅简化 |
| 稳定性 | 中等 | 高 | 显著提升 |
| 可维护性 | 低 | 高 | 显著提升 |
| 核心功能 | 完整 | 完整 | 保持不变 |

## 🔗 相关链接

- [rclone官方文档](https://rclone.org/docs/)
- [123网盘官网](https://www.123pan.com/)
- [115网盘官网](https://115.com/)

---

**注意**：本文档适用于简化后的新版本。如果您使用的是旧版本，请优先阅读 [迁移指南](migration-guide.md)。
