# 123网盘和115网盘后端测试验证指南

## 概述

本指南提供了详细的测试步骤，帮助用户验证简化后的123网盘和115网盘后端是否正常工作。

## 前置条件

### 环境要求
- rclone 版本：v1.60+ （推荐 v1.65+）
- 操作系统：Linux、macOS、Windows
- 网络连接：稳定的互联网连接

### 账号准备
- **123网盘**：有效的API客户端ID和密钥
- **115网盘**：有效的登录Cookie（UID、CID、SEID）

## 基础功能测试

### 1. 后端注册验证
```bash
# 检查后端是否正确注册
rclone help backend 123
rclone help backend 115

# 预期结果：显示后端帮助信息，无错误
```

### 2. 配置验证
```bash
# 检查配置是否正确
rclone config show 123
rclone config show 115

# 预期结果：显示配置信息，包含必要的认证参数
```

### 3. 连接测试
```bash
# 测试基本连接
rclone about 123:
rclone about 115:

# 预期结果：显示存储空间信息，无连接错误
```

## 核心功能测试

### 1. 目录列表测试
```bash
# 测试根目录列表
rclone ls 123: --max-depth 1
rclone ls 115: --max-depth 1

# 测试子目录列表
rclone ls 123:test/ --max-depth 1
rclone ls 115:test/ --max-depth 1

# 预期结果：正确显示文件和目录列表
```

### 2. 文件上传测试
```bash
# 创建测试文件
echo "Hello, World!" > test_small.txt
dd if=/dev/zero of=test_large.bin bs=1M count=100  # 100MB测试文件

# 小文件上传测试
rclone copy test_small.txt 123:test/
rclone copy test_small.txt 115:test/

# 大文件上传测试（分片上传）
rclone copy test_large.bin 123:test/
rclone copy test_large.bin 115:test/

# 预期结果：文件成功上传，无错误
```

### 3. 文件下载测试
```bash
# 下载测试文件
rclone copy 123:test/test_small.txt ./download_123/
rclone copy 115:test/test_small.txt ./download_115/

rclone copy 123:test/test_large.bin ./download_123/
rclone copy 115:test/test_large.bin ./download_115/

# 验证文件完整性
diff test_small.txt ./download_123/test_small.txt
diff test_small.txt ./download_115/test_small.txt

# 预期结果：文件下载成功，内容一致
```

### 4. 目录操作测试
```bash
# 创建目录
rclone mkdir 123:test/subdir
rclone mkdir 115:test/subdir

# 移动文件
rclone move 123:test/test_small.txt 123:test/subdir/
rclone move 115:test/test_small.txt 115:test/subdir/

# 删除目录
rclone rmdir 123:test/subdir
rclone rmdir 115:test/subdir

# 预期结果：目录操作成功执行
```

## 性能测试

### 1. 并发上传测试
```bash
# 123网盘并发上传测试
rclone sync ./test_files/ 123:performance_test/ \
  --123-max-concurrent-uploads=8 \
  --123-chunk-size=100M \
  --transfers=4 \
  --stats=1s

# 115网盘并发上传测试
rclone sync ./test_files/ 115:performance_test/ \
  --115-fast-upload=true \
  --115-chunk-size=50M \
  --transfers=4 \
  --stats=1s

# 预期结果：显示传输统计，速度合理
```

### 2. 并发下载测试
```bash
# 123网盘并发下载测试
rclone sync 123:performance_test/ ./download_123/ \
  --123-max-concurrent-downloads=6 \
  --transfers=4 \
  --stats=1s

# 115网盘并发下载测试
rclone sync 115:performance_test/ ./download_115/ \
  --transfers=4 \
  --stats=1s

# 预期结果：显示传输统计，速度合理
```

### 3. 秒传功能测试
```bash
# 创建相同内容的文件
cp test_large.bin test_duplicate.bin

# 上传原文件
rclone copy test_large.bin 123:test/

# 上传重复文件（应该触发秒传）
rclone -vv copy test_duplicate.bin 123:test/

# 预期结果：日志中显示秒传成功信息
```

## Backend Commands 测试

### 1. 可用命令测试
```bash
# 123网盘可用命令
rclone backend help 123:

# 115网盘可用命令
rclone backend help 115:

# 预期结果：显示可用的backend命令列表
```

### 2. 下载URL获取测试
```bash
# 测试获取下载URL
rclone backend getdownloadurlua 123: "/test/test_small.txt" "TestAgent/1.0"
rclone backend getdownloadurlua 115: "/test/test_small.txt" "TestAgent/1.0"

# 预期结果：返回有效的下载URL
```

### 3. 已移除命令验证
```bash
# 验证已移除的命令不可用
rclone backend cache-info 123: 2>&1 | grep -q "unknown command"
rclone backend cache-cleanup 115: 2>&1 | grep -q "unknown command"

# 预期结果：提示命令不存在
```

## 缓存功能测试

### 1. 目录缓存测试
```bash
# 测试目录缓存
time rclone ls 123:test/  # 第一次调用
time rclone ls 123:test/  # 第二次调用（应该更快）

# 使用不同的缓存时间
rclone ls 123:test/ --dir-cache-time=1m
rclone ls 123:test/ --dir-cache-time=10m

# 预期结果：第二次调用明显更快，缓存生效
```

### 2. VFS缓存测试（如果使用mount）
```bash
# 挂载测试
mkdir -p ./mount_123 ./mount_115
rclone mount 123: ./mount_123 \
  --vfs-cache-mode=writes \
  --vfs-cache-max-size=1G \
  --daemon

# 测试文件操作
echo "test" > ./mount_123/test_vfs.txt
cat ./mount_123/test_vfs.txt

# 卸载
fusermount -u ./mount_123  # Linux
# umount ./mount_123       # macOS

# 预期结果：文件操作正常，缓存生效
```

## 错误处理测试

### 1. 网络错误恢复测试
```bash
# 使用较小的超时时间测试重试
rclone ls 123: --timeout=5s --retries=3 -vv

# 预期结果：网络错误时自动重试
```

### 2. 认证错误测试
```bash
# 使用错误的认证信息
rclone ls 123: --123-client-id="invalid" 2>&1

# 预期结果：显示清晰的认证错误信息
```

## 兼容性测试

### 1. 配置文件兼容性
```bash
# 测试包含已废弃参数的配置文件
# 在配置文件中添加已废弃的参数，验证是否被忽略
rclone config show 123

# 预期结果：已废弃参数被忽略，不影响正常功能
```

### 2. 命令行参数兼容性
```bash
# 测试已废弃的命令行参数
rclone ls 123: --123-cache-max-size=100M 2>&1

# 预期结果：提示参数不存在，但不影响核心功能
```

## 性能基准测试

### 1. 上传性能基准
```bash
# 创建测试文件集
mkdir -p benchmark_files
for i in {1..10}; do
    dd if=/dev/zero of=benchmark_files/file_${i}.bin bs=1M count=50
done

# 执行基准测试
time rclone sync benchmark_files/ 123:benchmark/ --stats=1s
time rclone sync benchmark_files/ 115:benchmark/ --stats=1s

# 记录传输速度和时间
```

### 2. 下载性能基准
```bash
# 下载基准测试
time rclone sync 123:benchmark/ ./download_benchmark_123/ --stats=1s
time rclone sync 115:benchmark/ ./download_benchmark_115/ --stats=1s

# 记录传输速度和时间
```

## 故障排除

### 常见问题检查清单

1. **配置问题**
   - [ ] 认证信息正确
   - [ ] 网络连接正常
   - [ ] rclone版本兼容

2. **性能问题**
   - [ ] 并发参数合理
   - [ ] 分片大小适当
   - [ ] 网络带宽充足

3. **功能问题**
   - [ ] 后端正确注册
   - [ ] 命令语法正确
   - [ ] 权限设置正确

### 日志分析
```bash
# 获取详细日志
rclone -vv --log-file=rclone.log ls 123:

# 分析关键信息
grep -E "(ERROR|WARN|INFO)" rclone.log
grep -E "(123|115)" rclone.log | head -20
```

## 测试报告模板

```
测试环境：
- rclone版本：
- 操作系统：
- 网络环境：

测试结果：
- 基础功能：✅/❌
- 上传功能：✅/❌
- 下载功能：✅/❌
- 并发性能：✅/❌
- 秒传功能：✅/❌
- Backend命令：✅/❌

性能数据：
- 上传速度：XX MB/s
- 下载速度：XX MB/s
- 秒传成功率：XX%

问题记录：
- 问题描述
- 错误信息
- 解决方案
```

---

**注意**：测试过程中如遇到问题，请参考 [迁移指南](migration-guide.md) 或查看详细日志进行排查。
