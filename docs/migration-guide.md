# 123网盘和115网盘后端迁移指南

## 概述

本指南帮助用户从旧版本的123网盘和115网盘后端迁移到简化后的新版本。新版本移除了过度设计的功能，使后端更加符合rclone标准，提高了稳定性和可维护性。

## 主要变更概述

### 🔄 移除的功能
- **自定义缓存系统**：移除了BadgerDB持久化缓存，改用rclone标准dircache
- **统一错误处理器**：移除了UnifiedErrorHandler，改用rclone标准错误处理
- **复杂配置选项**：简化了缓存相关的配置参数
- **过度抽象组件**：移除了统一组件管理器和复杂的初始化逻辑

### ✅ 保留的功能
- **核心上传下载**：所有基本文件操作功能完全保留
- **并发传输**：自有的并发上传下载机制保持不变
- **秒传功能**：MD5/SHA1哈希秒传功能完全保留
- **rclone标准缓存**：使用rclone标准的dircache进行目录缓存

## 配置迁移指南

### 123网盘配置迁移

#### 移除的配置选项
```bash
# 以下配置选项已移除，无需配置
--123-cache-max-size=500M              # 移除：自定义缓存大小
--123-cache-target-size=400M           # 移除：缓存清理目标大小
--123-enable-smart-cleanup=true        # 移除：智能缓存清理
--123-cleanup-strategy=lru             # 移除：缓存清理策略
```

#### 保留的配置选项
```bash
# 核心认证配置（必需）
--123-client-id="your_client_id"
--123-client-secret="your_client_secret"
--123-token="oauth_token_json"

# 性能配置（推荐）
--123-max-concurrent-uploads=8         # 最大并发上传数
--123-max-concurrent-downloads=6       # 最大并发下载数
--123-chunk-size=100M                  # 分片大小
--123-upload-pacer-min-sleep=50ms      # 上传API调用间隔
--123-download-pacer-min-sleep=50ms    # 下载API调用间隔
```

### 115网盘配置迁移

#### 移除的配置选项
```bash
# 以下配置选项已移除，无需配置
--115-cache-max-size=500M              # 移除：自定义缓存大小
--115-cache-target-size=400M           # 移除：缓存清理目标大小
--115-enable-smart-cleanup=true        # 移除：智能缓存清理
--115-cleanup-strategy=lru             # 移除：缓存清理策略
```

#### 保留的配置选项
```bash
# 核心认证配置（必需）
--115-uid="your_uid"
--115-cid="your_cid"
--115-seid="your_seid"

# 性能配置（推荐）
--115-fast-upload=true                 # 启用智能上传
--115-chunk-size=50M                   # 分片大小
--115-upload-hash-only=false           # 是否仅尝试秒传
--115-hash-memory-limit=50M            # 哈希计算内存限制
```

## Backend Commands 变更

### 移除的命令
以下backend命令已移除，不再可用：
```bash
# 123网盘移除的命令
rclone backend cache-info 123:         # 移除：缓存信息查看
rclone backend cache-cleanup 123:      # 移除：手动缓存清理
rclone backend cache-stats 123:        # 移除：缓存统计信息
rclone backend cache-config 123:       # 移除：缓存配置查看
rclone backend cache-reset 123:        # 移除：缓存配置重置

# 115网盘移除的命令
rclone backend cache-info 115:         # 移除：缓存信息查看
rclone backend cache-cleanup 115:      # 移除：手动缓存清理
rclone backend cache-stats 115:        # 移除：缓存统计信息
rclone backend cache-config 115:       # 移除：缓存配置查看
rclone backend cache-reset 115:        # 移除：缓存配置重置
```

### 保留的命令
以下backend命令继续可用：
```bash
# 123网盘保留的命令
rclone backend getdownloadurlua 123: "/path/to/file" "UserAgent"
rclone backend media-sync 123:         # 媒体库同步功能

# 115网盘保留的命令
rclone backend getdownloadurlua 115: "/path/to/file" "UserAgent"
```

## 迁移步骤

### 步骤1：备份现有配置
```bash
# 备份rclone配置文件
cp ~/.config/rclone/rclone.conf ~/.config/rclone/rclone.conf.backup

# 备份自定义脚本中的rclone命令
# 记录当前使用的所有--123-*和--115-*参数
```

### 步骤2：更新rclone版本
```bash
# 更新到包含简化后端的rclone版本
# 具体更新方法取决于您的安装方式
```

### 步骤3：清理配置文件
```bash
# 编辑rclone配置文件，移除已废弃的配置选项
# 或者重新运行rclone config来重新配置
rclone config
```

### 步骤4：更新脚本和命令
```bash
# 检查并更新所有使用rclone的脚本
# 移除已废弃的配置参数
# 移除已废弃的backend命令调用
```

### 步骤5：测试验证
```bash
# 测试基本功能
rclone ls 123:
rclone ls 115:

# 测试上传下载
rclone copy local_file 123:remote_path
rclone copy 123:remote_file local_path

# 测试保留的backend命令
rclone backend getdownloadurlua 123: "/test/file.mp4" "VidHub/1.7.24"
```

## 性能优化建议

### 新的推荐配置

#### 123网盘高性能配置
```bash
rclone sync local: 123:remote \
  --123-max-concurrent-uploads=8 \
  --123-max-concurrent-downloads=6 \
  --123-chunk-size=100M \
  --123-upload-pacer-min-sleep=50ms \
  --123-download-pacer-min-sleep=50ms \
  --transfers=4 \
  --checkers=8
```

#### 115网盘高性能配置
```bash
rclone sync local: 115:remote \
  --115-fast-upload=true \
  --115-chunk-size=50M \
  --115-hash-memory-limit=50M \
  --transfers=4 \
  --checkers=8
```

### 缓存策略变更
- **旧版本**：使用自定义BadgerDB持久化缓存
- **新版本**：使用rclone标准dircache，配合以下参数：
```bash
--dir-cache-time=5m                    # 目录缓存时间
--vfs-cache-mode=writes                # VFS缓存模式（如果使用mount）
--vfs-cache-max-size=1G                # VFS缓存大小（如果使用mount）
```

## 故障排除

### 常见问题

#### Q1: 提示"unknown flag"错误
**问题**：使用已移除的配置参数时出现错误
```bash
Error: unknown flag: --123-cache-max-size
```
**解决方案**：从命令中移除已废弃的配置参数

#### Q2: Backend命令不可用
**问题**：使用已移除的backend命令时出现错误
```bash
Error: unknown command "cache-info" for "rclone backend"
```
**解决方案**：停止使用已移除的backend命令，使用rclone标准功能替代

#### Q3: 性能下降
**问题**：迁移后感觉性能有所下降
**解决方案**：
1. 调整rclone标准参数：`--transfers`、`--checkers`
2. 使用推荐的高性能配置
3. 根据网络情况调整`pacer-min-sleep`参数

#### Q4: 缓存相关功能缺失
**问题**：无法查看缓存统计或手动清理缓存
**解决方案**：
1. 使用rclone标准的`--dir-cache-time`参数控制缓存
2. 重启rclone进程来清理内存缓存
3. 使用`rclone rc vfs/forget`清理VFS缓存（如果使用mount）

## 兼容性说明

### 向后兼容性
- **配置文件**：旧的配置文件仍然可用，只是已废弃的参数会被忽略
- **基本功能**：所有核心上传下载功能保持100%兼容
- **API接口**：与网盘服务的API交互方式没有变化

### 不兼容变更
- **自定义缓存**：无法再使用自定义缓存功能
- **Backend命令**：部分backend命令不再可用
- **错误处理**：错误消息格式可能略有不同

## 获取帮助

如果在迁移过程中遇到问题，可以：

1. **查看日志**：使用`-vv`参数获取详细日志
```bash
rclone -vv sync local: 123:remote
```

2. **检查配置**：使用`rclone config show`查看当前配置
```bash
rclone config show 123
```

3. **测试连接**：使用`rclone about`测试后端连接
```bash
rclone about 123:
```

4. **查看帮助**：使用help命令查看最新参数说明
```bash
rclone help backend 123
rclone help backend 115
```

---

**注意**：本迁移指南基于当前简化版本编写。如果您使用的是更早的版本，建议先查看相应版本的文档。
